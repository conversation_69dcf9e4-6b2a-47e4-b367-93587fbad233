---
type: "always_apply"
---

# MasterCRM Kodlama Standartları

## Genel Prensipler

### 1. Modüler Monolit Yapısı
- Her modül kendi içinde bağımsız olmalı (Users, Customers, Conversations, etc.)
- Modüller arası iletişim sadece iyi tanımlanmış arayüzler aracılığıyla yapılmalı
- Shared katmanı minimum düzeyde tutulmalı
- Mo<PERSON><PERSON><PERSON> bağımlılığı gevşek olmalı

### 2. Vertical Slice Architecture
- Her feature kendi klasöründe organize edilmeli
- Command/Query, Handler, Validator ve Endpoint aynı klasörde bulunmalı
- Teknolojiye göre değil, feature'a göre organize et (command/query klasörleri kullanma)

### 3. Clean Architecture Katmanları
```
Module/
├── Application/          # Use cases, commands, queries
│   ├── FeatureName/
│   │   ├── Command.cs
│   │   ├── CommandHandler.cs
│   │   ├── CommandValidator.cs
│   │   └── Endpoint.cs
├── Domain/              # Business logic, entities, events
├── Infrastructure/      # External concerns, data access
└── ModuleInitializer.cs # DI configuration
```

## C# Kodlama Standartları

### 1. Class Yapısı
- Primary Constructor kullan
- Record types uygun yerlerde kullan
- Nullable reference types aktif tut

```csharp
// ✅ Doğru
public class CreateCustomerCommand(string Name, string Email);

// ❌ Yanlış
public class CreateCustomerCommand
{
    public CreateCustomerCommand(string name, string email)
    {
        Name = name;
        Email = email;
    }
    public string Name { get; set; }
    public string Email { get; set; }
}
```

### 2. Namespace Yapısı
- Namespace'ler MasterCRM.Modules ile başlamasın
- Modül adı ile başlasın: `Customers.Application.CreateCustomer`

### 3. Domain Entities
- Rich Domain Models kullan
- Domain Events implement et
- IEntity interface'ini implement et
- BaseEntity'den türet

```csharp
public class Customer : BaseEntity, IEntity
{
    private readonly List<IDomainEvent> _domainEvents = [];

    public List<IDomainEvent> DomainEvents => [.. _domainEvents];

    public void BusinessMethod()
    {
        // İş mantığı
        Raise(new CustomerCreatedEvent(Id));
    }

    public void Raise(IDomainEvent domainEvent)
    {
        _domainEvents.Add(domainEvent);
    }

    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }
}
```

## CQRS ve MediatR Standartları

### 1. Command/Query Yapısı
- Her dosyada bir Command/Query
- MediatR ile CQRS deseni kullan
- Result Pattern kullan

```csharp
// Command
public record CreateCustomerCommand(
    string Name,
    string Email,
    string Phone
) : IRequest<Result<Guid>>;

// Query
public record GetCustomerQuery(Guid Id) : IRequest<Result<CustomerDto>>;
```

### 2. Handler Yapısı
```csharp
public class CreateCustomerCommandHandler(
    ICustomersDbContext context)
    : IRequestHandler<CreateCustomerCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(
        CreateCustomerCommand request,
        CancellationToken cancellationToken)
    {
        // Implementation
    }
}
```

### 3. Validation
- FluentValidation kullan
- Async validation gerektiğinde MustAsync kullan
- Türkçe hata mesajları kullan

```csharp
public class CreateCustomerCommandValidator : AbstractValidator<CreateCustomerCommand>
{
    public CreateCustomerCommandValidator(ICustomersDbContext context)
    {
        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("E-posta adresi boş olamaz.")
            .EmailAddress().WithMessage("Geçerli bir e-posta adresi giriniz.")
            .MustAsync(async (email, ct) =>
                !await context.Customers.AnyAsync(c => c.Email == email, ct))
            .WithMessage("Bu e-posta adresi zaten kullanılıyor.");
    }
}
```

## API Endpoint Standartları

### 1. Minimal API Kullanımı
- Controller kullanma, sadece Minimal API
- Endpoint'ler feature klasöründe bulunmalı
- Produces method'ları Result<T> formatında ekle

```csharp
public class CreateCustomerEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/customers", async (
            CreateCustomerCommand command,
            ISender sender) =>
        {
            var result = await sender.Send(command);
            return result.Match(
                onSuccess: Results.Ok,
                onFailure: Results.BadRequest);
        })
        .WithTags("Customers")
        .Produces<Result<Guid>>(StatusCodes.Status200OK)
        .Produces<Result>(StatusCodes.Status400BadRequest);
    }
}
```

### 2. HTTP Test Dosyaları
- Test dosyaları `@test/Modules/{ModuleName}/{FeatureName}.http` formatında
- Variables kullan
- Tüm CRUD operasyonları test edilmeli

## Veritabanı ve Entity Framework

### 1. DbContext Yapısı
- Her modül kendi DbContext'ine sahip
- IBaseDbContext'ten türet
- Interface kullan

### 2. Migration
- Otomatik migration kullanma
- Migration dosyalarını manuel kontrol et
- Seed data için HasData kullan

## Dosya ve Klasör Organizasyonu

### 1. Feature Organizasyonu
```
Application/
├── Customers/
│   ├── CreateCustomer/
│   │   ├── CreateCustomerCommand.cs
│   │   ├── CreateCustomerCommandHandler.cs
│   │   ├── CreateCustomerCommandValidator.cs
│   │   └── CreateCustomerEndpoint.cs
│   └── GetCustomer/
│       ├── GetCustomerQuery.cs
│       ├── GetCustomerQueryHandler.cs
│       └── GetCustomerEndpoint.cs
```

### 2. Shared Components
- DTO'lar feature klasöründe veya modül seviyesinde
- Shared sadece gerçekten paylaşılan kod için
- Extension method'lar Utilities klasöründe

## Error Handling ve Logging

### 1. Result Pattern
- Tüm business operations Result<T> dönsün
- Exception fırlatma, Result.Failure kullan
- Validation Error'lar için ValidationError kullan

### 2. Domain Events
- Business olayları için Domain Events kullan
- Event handler'lar Infrastructure katmanında
- Async event processing için MediatR kullan

## Test Standartları

### 1. HTTP Tests
- Her endpoint için HTTP test dosyası
- Variables ile parametrize et
- Positive ve negative case'leri test et

### 2. Unit Tests
- Handler'lar için unit test yaz
- Validator'lar için unit test yaz
- Domain logic için unit test yaz

## Performance ve Caching

### 1. Database Queries
- Projection kullan (Select sadece gerekli alanları)
- AsNoTracking read-only query'ler için
- Pagination implement et (PagedResult kullan)

### 2. Caching
- HybridCache kullan
- Cache key'leri consistent tut
- Cache invalidation stratejisi belirle
