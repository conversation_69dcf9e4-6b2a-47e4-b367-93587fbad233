# MasterCRM Code Review Checklist

## Genel Kontroller

### ✅ Kod Kalitesi
- [ ] Kod okunabilir ve anlaşılır mı?
- [ ] Naming conventions'a uygun mu? (PascalCase, camelCase)
- [ ] Primary constructor kullanılmış mı?
- [ ] Nullable reference types doğru kullanılmış mı?
- [ ] Magic number'lar ve string'ler constant olarak tanımlanmış mı?
- [ ] Dead code var mı? (Kullanılmayan method, property, class)

### ✅ Architecture ve Design
- [ ] Vertical Slice Architecture'a uygun mu?
- [ ] Feature'lar doğru klasörlerde organize edilmiş mi?
- [ ] Command/Query/Handler/Validator/Endpoint aynı klasörde mi?
- [ ] Modüler monolit yapısına uygun mu?
- [ ] Separation of concerns prensibine uygun mu?
- [ ] SOLID prensiplerine uygun mu?

## Domain Katmanı

### ✅ Entity Design
- [ ] Rich Domain Model kullanılmış mı?
- [ ] BaseEntity'den türetilmiş mi?
- [ ] IEntity interface'i implement edilmiş mi?
- [ ] Domain Events doğru implement edilmiş mi?
- [ ] Business logic entity içinde mi? (Anemic model değil)
- [ ] Value Object'ler uygun yerlerde kullanılmış mı?

### ✅ Domain Events
- [ ] Domain Events BaseDomainEvent'ten türetilmiş mi?
- [ ] Event naming convention'a uygun mu? (PastTense + DomainEvent)
- [ ] Event'ler immutable mı? (record type)
- [ ] Event handler'lar Infrastructure katmanında mı?

## Application Katmanı

### ✅ CQRS Implementation
- [ ] Command/Query IRequest<Result<T>> implement ediyor mu?
- [ ] Handler IRequestHandler implement ediyor mu?
- [ ] Result Pattern kullanılmış mı?
- [ ] Exception fırlatılmıyor, Result.Failure kullanılıyor mu?
- [ ] Primary constructor kullanılmış mı?

### ✅ Validation
- [ ] FluentValidation kullanılmış mı?
- [ ] Validator AbstractValidator'dan türetilmiş mi?
- [ ] Async validation gerektiğinde MustAsync kullanılmış mı?
- [ ] Hata mesajları Türkçe mi?
- [ ] Business rule validation'lar domain'de mi?

### ✅ DTOs ve Mapping
- [ ] DTO'lar sadece data transfer için kullanılmış mı?
- [ ] Mapping logic'i uygun yerde mi?
- [ ] AutoMapper yerine manuel mapping kullanılmış mı?
- [ ] Projection kullanılmış mı? (Select sadece gerekli alanları)

## API Endpoints

### ✅ Minimal API
- [ ] Controller kullanılmamış, Minimal API kullanılmış mı?
- [ ] IEndpoint interface'i implement edilmiş mi?
- [ ] Endpoint feature klasöründe mi?
- [ ] HTTP method'lar doğru kullanılmış mı? (GET, POST, PUT, DELETE, PATCH)
- [ ] Route naming convention'a uygun mu?

### ✅ Response Handling
- [ ] Result.Match kullanılmış mı?
- [ ] Produces method'ları Result<T> formatında mı?
- [ ] HTTP status code'lar doğru mu?
- [ ] Error response'lar standardize edilmiş mi?
- [ ] WithTags kullanılmış mı?

### ✅ Request Handling
- [ ] Model binding doğru çalışıyor mu?
- [ ] Validation otomatik çalışıyor mu?
- [ ] CancellationToken kullanılmış mı?
- [ ] Async/await doğru kullanılmış mı?

## Database ve Entity Framework

### ✅ DbContext
- [ ] Her modül kendi DbContext'ine sahip mi?
- [ ] IBaseDbContext'ten türetilmiş mi?
- [ ] Interface kullanılmış mı?
- [ ] DbSet'ler doğru tanımlanmış mı?

### ✅ Entity Configuration
- [ ] Fluent API configuration'lar ayrı dosyalarda mı?
- [ ] IEntityTypeConfiguration implement edilmiş mi?
- [ ] Index'ler performance-critical query'ler için tanımlanmış mı?
- [ ] Foreign key relationship'ler doğru mu?
- [ ] Cascade delete davranışları uygun mu?

### ✅ Queries
- [ ] AsNoTracking read-only query'ler için kullanılmış mı?
- [ ] Projection kullanılmış mı?
- [ ] N+1 problem'i var mı?
- [ ] Include'lar gerekli mi?
- [ ] Pagination implement edilmiş mi?

## Performance ve Güvenlik

### ✅ Performance
- [ ] Database query'leri optimize edilmiş mi?
- [ ] Caching uygun yerlerde kullanılmış mı?
- [ ] Memory leak riski var mı?
- [ ] Async operations doğru kullanılmış mı?
- [ ] Large dataset'ler için pagination var mı?

### ✅ Security
- [ ] Input validation yapılmış mı?
- [ ] SQL injection riski var mı?
- [ ] Authorization check'leri var mı?
- [ ] Sensitive data log'lanmıyor mu?
- [ ] HTTPS kullanılıyor mu?

## Testing

### ✅ HTTP Tests
- [ ] Her endpoint için HTTP test dosyası var mı?
- [ ] Test dosyaları doğru lokasyonda mı? (@test/Modules/{ModuleName}/{FeatureName}.http)
- [ ] Variables kullanılmış mı?
- [ ] Positive ve negative case'ler test edilmiş mi?
- [ ] Authentication gerekli endpoint'ler için token var mı?

### ✅ Unit Tests
- [ ] Business logic için unit test var mı?
- [ ] Validator'lar için unit test var mı?
- [ ] Edge case'ler test edilmiş mi?
- [ ] Mock'lar doğru kullanılmış mı?

## Dokümantasyon

### ✅ Code Documentation
- [ ] Public API'ler için XML comment var mı?
- [ ] Complex business logic açıklanmış mı?
- [ ] TODO comment'lar temizlenmiş mi?
- [ ] Obsolete code işaretlenmiş mi?

### ✅ API Documentation
- [ ] Swagger/OpenAPI documentation güncel mi?
- [ ] Endpoint açıklamaları var mı?
- [ ] Request/Response example'ları var mı?
- [ ] Error response'lar dokümante edilmiş mi?

## Deployment Hazırlığı

### ✅ Configuration
- [ ] appsettings.json dosyaları Content klasöründe mi?
- [ ] Environment-specific configuration'lar var mı?
- [ ] Sensitive data environment variable'larda mı?
- [ ] Connection string'ler güvenli mi?

### ✅ Migration
- [ ] Database migration'ları kontrol edilmiş mi?
- [ ] Seed data güncellenmiş mi?
- [ ] Backward compatibility korunmuş mu?
- [ ] Production deployment planı hazır mı?

## Final Checklist

### ✅ Before Merge
- [ ] Tüm testler geçiyor mu?
- [ ] Build başarılı mı?
- [ ] Code coverage yeterli mi?
- [ ] Performance regression yok mu?
- [ ] Security vulnerability yok mu?
- [ ] Breaking change var mı?
- [ ] Documentation güncellenmiş mi?
- [ ] Migration script'leri hazır mı?

### ✅ Post-Merge Actions
- [ ] Deployment pipeline çalıştırılacak mı?
- [ ] Monitoring dashboard'ları kontrol edilecek mi?
- [ ] User acceptance testing yapılacak mı?
- [ ] Rollback planı hazır mı?

---

## Review Notları

**Reviewer:** _______________
**Date:** _______________
**PR/Branch:** _______________

**Major Issues:**
- [ ]

**Minor Issues:**
- [ ]

**Suggestions:**
- [ ]

**Approval Status:**
- [ ] ✅ Approved
- [ ] ⚠️ Approved with minor changes
- [ ] ❌ Needs major changes