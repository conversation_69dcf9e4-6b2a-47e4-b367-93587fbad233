# MasterCRM Proje Yönergeleri

## Proje Yapısı ve Organizasyon

### 1. Solution Yapısı
```
MasterCRM/
├── src/
│   ├── Host/                    # Ana uygulama host'u
│   ├── Shared/                  # Paylaşılan kod
│   ├── Client/                  # React frontend
│   └── Modules/                 # İş modülleri
│       ├── Users/
│       ├── Customers/
│       ├── Conversations/
│       ├── Requests/
│       ├── DynamicForms/
│       ├── Sales/
│       ├── Tasks/
│       ├── General/
│       └── Calendar/
├── test/                        # Test dosyaları
├── docs/                        # Dokümantasyon
└── Content/                     # Statik dosyalar
```

### 2. Modül İç Yapısı
Her modül şu yapıya sahip olmalı:
```
ModuleName/
├── Application/                 # Use cases, CQRS
│   ├── Abstractions/           # Interfaces
│   ├── FeatureName/            # Vertical slices
│   └── Services/               # Application services
├── Domain/                     # Business logic
│   ├── Entities/
│   ├── Events/
│   ├── Enums/
│   └── Errors/
├── Infrastructure/             # External concerns
│   ├── Data/                   # EF configurations
│   └── External/               # 3rd party integrations
└── ModuleInitializer.cs        # DI registration
```

## Geliştirme İş Akışı

### 1. Yeni Feature Ekleme
1. **Analiz**: PRD.md ve RFC dokümanlarını incele
2. **Planlama**: Task management tools kullanarak plan yap
3. **Domain**: Entity ve business logic'i implement et
4. **Application**: Command/Query/Handler/Validator yaz
5. **Infrastructure**: Data access ve external service'leri implement et
6. **API**: Endpoint'leri oluştur
7. **Test**: HTTP test dosyalarını yaz
8. **Dokümantasyon**: Gerekli dokümantasyonu güncelle

### 2. Code Review Süreci
- Her PR için review-checklist.md'yi kontrol et
- Automated tests çalıştır
- Manual testing yap
- Performance impact'ini değerlendir

### 3. Database Değişiklikleri
- Migration'ları manuel olarak kontrol et
- Seed data'yı güncelle
- Backward compatibility'yi kontrol et
- Production deployment planını hazırla

## Teknoloji Kılavuzları

### 1. Backend (.NET 9)
- **Architecture**: Modular Monolith + Vertical Slice
- **Patterns**: CQRS, Result Pattern, Domain Events
- **ORM**: Entity Framework Core
- **Validation**: FluentValidation
- **API**: Minimal API (Controller kullanma)
- **Caching**: HybridCache
- **Background Jobs**: Quartz.NET

### 2. Frontend (React)
- **UI Framework**: Ant Design
- **Styling**: Tailwind CSS
- **State Management**: Context API / Zustand
- **HTTP Client**: Axios
- **Form Handling**: Ant Design Forms

### 3. Database (SQL Server)
- **Migrations**: Code-first approach
- **Indexing**: Performance-critical query'ler için
- **Constraints**: Business rule'ları database seviyesinde
- **Backup**: Automated backup strategy

## Güvenlik Yönergeleri

### 1. Authentication & Authorization
- JWT token based authentication
- Role-based authorization
- Permission-based access control
- Secure password policies

### 2. Data Protection
- Sensitive data encryption
- GDPR compliance
- Audit logging
- Data retention policies

### 3. API Security
- Input validation
- SQL injection prevention
- XSS protection
- Rate limiting

## Performance Yönergeleri

### 1. Database Performance
- Query optimization
- Proper indexing
- Connection pooling
- Pagination for large datasets

### 2. Caching Strategy
- Application-level caching
- Database query caching
- Static file caching
- CDN usage for frontend assets

### 3. Monitoring
- Application performance monitoring
- Error tracking
- Resource usage monitoring
- User experience metrics

## Deployment ve DevOps

### 1. Environment Management
- Development
- Staging
- Production
- Configuration management per environment

### 2. CI/CD Pipeline
- Automated testing
- Code quality checks
- Security scanning
- Automated deployment

### 3. Monitoring ve Logging
- Centralized logging
- Error tracking
- Performance monitoring
- Health checks

## Dokümantasyon Standartları

### 1. Code Documentation
- XML comments for public APIs
- README files for each module
- Architecture decision records (ADRs)

### 2. API Documentation
- OpenAPI/Swagger documentation
- Endpoint examples
- Error response documentation

### 3. User Documentation
- Feature documentation
- User guides
- Troubleshooting guides

## Kalite Güvencesi

### 1. Testing Strategy
- Unit tests for business logic
- Integration tests for APIs
- HTTP tests for endpoints
- End-to-end tests for critical flows

### 2. Code Quality
- Static code analysis
- Code coverage metrics
- Performance benchmarks
- Security vulnerability scanning

### 3. Review Process
- Peer code reviews
- Architecture reviews
- Security reviews
- Performance reviews

## Bakım ve Sürdürülebilirlik

### 1. Technical Debt Management
- Regular refactoring
- Dependency updates
- Performance optimization
- Security patches

### 2. Knowledge Management
- Team knowledge sharing
- Documentation maintenance
- Best practices documentation
- Lessons learned documentation

### 3. Continuous Improvement
- Regular retrospectives
- Process improvements
- Tool evaluations
- Technology updates