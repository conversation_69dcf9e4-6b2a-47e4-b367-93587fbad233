﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddNotificationFieldsToUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "EmailPermission",
                schema: "Users",
                table: "User",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "MobilePushPermission",
                schema: "Users",
                table: "User",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "NotificationToken",
                schema: "Users",
                table: "User",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "SMSPermission",
                schema: "Users",
                table: "User",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "WebPushPermission",
                schema: "Users",
                table: "User",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EmailPermission",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "MobilePushPermission",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "NotificationToken",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "SMSPermission",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "WebPushPermission",
                schema: "Users",
                table: "User");
        }
    }
}
