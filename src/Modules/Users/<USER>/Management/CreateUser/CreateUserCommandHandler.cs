using System.ComponentModel.DataAnnotations;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;

namespace Users.Application.Management.CreateUser;

public class CreateUserCommandHandler(
    IUserStore<User> userStore,
    UserManager<User> userManager,
    IUserDbContext dbContext,
    IThreeCXUserService threeCXUserService
) : IRequestHandler<CreateUserCommand, Result<Guid>>
{
    private readonly IUserStore<User> _userStore = userStore;
    private readonly UserManager<User> _userManager = userManager;
    private readonly IUserDbContext _dbContext = dbContext;
    private readonly IThreeCXUserService _threeCXUserService = threeCXUserService;

    public async Task<Result<Guid>> Handle(CreateUserCommand command, CancellationToken cancellationToken)
    {
        var three3cUsers = await _threeCXUserService.GetUsers();
        if (three3cUsers.Value.Any(x => x.Number == command.ThreeCXExtension))
        {
            return Result.Failure<Guid>("3CX extension numarası daha önce kayıt edilmiştir.");
        }
        if (three3cUsers.Value.Any(x => x.Email == command.Email || x.EmailAddress == command.Email))
        {
            return Result.Failure<Guid>("3CX email adresi daha önce kayıt edilmiştir.");
        }
        if (!_userManager.SupportsUserEmail)
        {
            return Result.Failure<Guid>($"Register API requires a user store with email support.");
        }
        var _emailStore = (IUserEmailStore<User>)_userStore;
        var email = command.Email;
        var _emailAddressAttribute = new EmailAddressAttribute();
        if (string.IsNullOrEmpty(email) || !_emailAddressAttribute.IsValid(email))
        {
            return Result.Failure<Guid>(UserErrors.NotValidEmail);
        }
        if (await _dbContext.Users.AnyAsync(u => u.Email == command.Email, cancellationToken))
        {
            return Result.Failure<Guid>(UserErrors.EmailNotUnique);
        }
        var user = new User
        {
            Active = command.Active,
            Name = command.Name,
            Surname = command.Surname,
            PhoneNumber = command.PhoneNumber,
            PhonePrefix = command.PhonePrefix,
            EmailConfirmed = true,
            ThreeCXExtension = command.ThreeCXExtension,
            ThreeCXExternal = command.ThreeCXExternal,
            ThreeCXEnabled = command.ThreeCXEnabled,
            ThreeCXRecording = command.ThreeCXRecording,
            PhoneNumberConfirmed = false,
            NotificationToken = command.NotificationToken,
            SMSPermission = command.SMSPermission,
            EmailPermission = command.EmailPermission,
            MobilePushPermission = command.MobilePushPermission,
            WebPushPermission = command.WebPushPermission
        };
        user.Raise(new UserRegisteredDomainEvent(user.Id));
        await _userStore.SetUserNameAsync(user, email, CancellationToken.None);
        await _emailStore.SetEmailAsync(user, email, CancellationToken.None);
        var result = await _userManager.CreateAsync(user, command.Password);
        if (!result.Succeeded)
        {
            return Result.Failure<Guid>(CreateValidationError(result.Errors));
        }
        await _userManager.AddToRoleAsync(user, "USER");
        var roles = _dbContext.Roles.Where(r => command.RoleIds.Contains(r.Id)).ToList();
        foreach (var role in roles)
        {
            await _userManager.AddToRoleAsync(user, role.NormalizedName);
        }
        if (command.DepartmentIds != null)
        {
            var userDepartments = command.DepartmentIds.Select(x => new UserDepartment { DepartmentId = x, UserId = user.Id }).ToList();
            _dbContext.UserDepartment.AddRange(userDepartments);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        var threecxresult = await _threeCXUserService.CreateUser(user.Id, command.Password);
        if (!threecxresult.IsSuccess)
        {
            return Result.Failure<Guid>("Kullanıcı oluşturuldu ama 3cx e aktarılamadı: " + threecxresult.Error.Description);
        }
        var threecxUpdateResult = await _threeCXUserService.UpdateUser(user);
        return Result.Success(user.Id);
    }

    private static ValidationError CreateValidationError(IEnumerable<IdentityError> validationFailures) =>
        new([.. validationFailures.Select(f => Error.Problem(f.Code, f.Description))]);
}
