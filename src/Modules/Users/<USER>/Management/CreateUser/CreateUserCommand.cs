using MediatR;
using Shared.Application;

namespace Users.Application.Management.CreateUser;

public record CreateUserCommand(
    bool Active,
    string Name,
    string Surname,
    string Email,
    string Password,
    string PhoneNumber,
    string? PhonePrefix = default,
    string? ThreeCXExtension = default,
    string? ThreeCXEnabled = default,
    string? ThreeCXExternal = default,
    string? ThreeCXRecording = default,
    Guid[]? DepartmentIds = default,
    Guid[]? RoleIds = default,
    Dictionary<string, string>? AttributeData = null,
    string? NotificationToken = default,
    bool SMSPermission = false,
    bool EmailPermission = false,
    bool MobilePushPermission = false,
    bool WebPushPermission = false
) : IRequest<Result<Guid>>;
