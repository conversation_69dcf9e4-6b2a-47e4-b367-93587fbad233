using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;
using Users.Domain.Account.Events;

namespace Users.Application.Management.UpdateUser;

public class UpdateUserCommandHandler(
    IUserStore<User> userStore,
    UserManager<User> userManager,
    IUserDbContext context,
    IThreeCXUserService threeCXUserService
) : IRequestHandler<UpdateUserCommand, Result>
{
    private readonly IUserStore<User> _userStore = userStore;
    private readonly UserManager<User> _userManager = userManager;
    private readonly IUserDbContext _context = context;
    private readonly IThreeCXUserService _threeCXUserService = threeCXUserService;

    public async Task<Result> Handle(UpdateUserCommand request, CancellationToken cancellationToken)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

        if (user == null)
        {
            return Result.Failure(UserErrors.NotFoundError);
        }

        // Check if email is being changed and if it's already taken
        if (user.Email != request.Email)
        {
            var existingUser = await _userManager.FindByEmailAsync(request.Email);
            if (existingUser != null)
            {
                return Result.Failure(UserErrors.EmailNotUnique);
            }
            await _userStore.SetUserNameAsync(user, request.Email, CancellationToken.None);
            var _emailStore = (IUserEmailStore<User>)_userStore;
            await _emailStore.SetEmailAsync(user, request.Email, CancellationToken.None);
        }
        user.Name = request.Name;
        user.Surname = request.Surname;
        user.PhoneNumber = request.PhoneNumber;
        user.PhonePrefix = request.PhonePrefix;
        user.ThreeCXExtension = request.ThreeCXExtension;
        user.ThreeCXEnabled = request.ThreeCXEnabled;
        user.ThreeCXExternal = request.ThreeCXExternal;
        user.ThreeCXRecording = request.ThreeCXRecording;
        user.ChatURL = request.ChatURL;
        user.Active = request.Active;
        user.AttributeData = request.AttributeData;
        user.NotificationToken = request.NotificationToken;
        user.SMSPermission = request.SMSPermission;
        user.EmailPermission = request.EmailPermission;
        user.MobilePushPermission = request.MobilePushPermission;
        user.WebPushPermission = request.WebPushPermission;
        user.Raise(new UserUpdatedDomainEvent(
            user.Id,
            user.Email,
            user.Name,
            user.Surname,
            user.Active));
        await _context.SaveChangesAsync(cancellationToken);
        await _context.UserRoles.Where(x => x.UserId == request.Id).ExecuteDeleteAsync(cancellationToken: cancellationToken);
        if (request.RoleIds?.Length > 0)
        {
            var roles = _context.Roles.Where(r => request.RoleIds.Contains(r.Id)).ToList();
            foreach (var role in roles)
            {
                await _userManager.AddToRoleAsync(user, role.NormalizedName);
            }
        }
        if (request.DepartmentIds != null)
        {
            await _context.UserDepartment.Where(x => x.UserId == request.Id).ExecuteDeleteAsync(cancellationToken: cancellationToken);
            var userDepartments = request.DepartmentIds.Select(x => new UserDepartment { DepartmentId = x, UserId = user.Id }).ToList();
            _context.UserDepartment.AddRange(userDepartments);
            await _context.SaveChangesAsync(cancellationToken);
        }
        var threecxUpdateResult = await _threeCXUserService.UpdateUser(user);
        return Result.Success();
    }
}
