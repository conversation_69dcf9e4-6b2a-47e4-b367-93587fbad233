using MediatR;
using Shared.Application;

namespace Users.Application.Management.GetUser;

public record GetUserQuery(Guid Id) : IRequest<Result<GetUserResponse>>;

public record GetUserResponse(
    Guid Id,
    string? Email,
    string Name,
    string Surname,
    string PhoneNumber,
    string? PhonePrefix,
    string? ThreeCXExtension,
    string? ThreeCXEnabled,
    string? ThreeCXExternal,
    string? ThreeCXRecording,
    string? ChatURL,
    bool Active,
    bool EmailConfirmed,
    bool PhoneNumberConfirmed,
    DateTime InsertDate,
    DateTime? UpdateDate,
    List<RoleDto> Roles,
    List<UserDepartmentDto> Departments,
    Dictionary<string, string>? AttributeData,
    string? NotificationToken,
    bool SMSPermission,
    bool EmailPermission,
    bool MobilePushPermission,
    bool WebPushPermission
);

public record RoleDto(
    Guid Id,
    string Name
);

public record UserDepartmentDto(
    Guid Id,
    string Name
);
