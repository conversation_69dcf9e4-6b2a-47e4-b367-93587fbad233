using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Users.Application.Abstractions;
using Users.Domain.Account;

namespace Users.Application.Management.GetUser;

public class GetUserQueryHandler(
    IUserDbContext context
) : IRequestHandler<GetUserQuery, Result<GetUserResponse>>
{
    private readonly IUserDbContext _context = context;

    public async Task<Result<GetUserResponse>> <PERSON>le(GetUserQuery request, CancellationToken cancellationToken)
    {
        var user = await _context.Users
            .AsNoTracking()
            .Where(u => u.Id == request.Id)
            .Select(u => new GetUserResponse(
                u.Id,
                u.Email,
                u.Name,
                u.Surname,
                u.PhoneNumber ?? string.Empty,
                u.PhonePrefix,
                u.ThreeCXExtension,
                u.ThreeCXEnabled,
                u.ThreeCXExternal,
                u.ThreeCXRecording,
                u.ChatURL,
                u.Active,
                u.EmailConfirmed,
                u.PhoneNumberConfirmed,
                u.InsertDate,
                u.UpdateDate,
                u.UserRole.Select(x => new RoleDto(x.Role.Id, x.Role.Name)).ToList(),
                u.UserDepartment.Select(x => new UserDepartmentDto(x.Department.Id, x.Department.Name)).ToList(),
                u.AttributeData,
                u.NotificationToken,
                u.SMSPermission,
                u.EmailPermission,
                u.MobilePushPermission,
                u.WebPushPermission))
            .FirstOrDefaultAsync(cancellationToken);

        if (user == null)
        {
            return Result.Failure<GetUserResponse>(UserErrors.NotFoundError);
        }
        return Result.Success(user);
    }
}
